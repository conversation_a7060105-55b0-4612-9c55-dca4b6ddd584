import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './PriceScreen.module.css';
import IconButton from '../../common/button/IconButton';
import { IoArrowBack } from 'react-icons/io5';
import { usePayment } from '../../../hooks/payment/usePayment';
import { PaymentPlan } from '../../../types/payment';
import LoadingSpinner from '../../common/loading/LoadingSpinner';
import { Alert } from '@mui/material';
import CustomButton from '../../common/button/CustomButton';
import { getCurrentUser } from '../../../utils/auth/userHelpers';

const PriceScreen: React.FC = () => {
  const navigate = useNavigate();
  const {
    plans,
    isLoading,
    hasError,
    errorMessage,
    hasPlansError,
    plansErrorMessage,
    startPayment,
    formatPlanPrice,
    getPopularPlan,
    clearPaymentError,
  } = usePayment();

  const [selectedPlan, setSelectedPlan] = useState<PaymentPlan | null>(null);

  // Get current user's plan information
  const currentUser = getCurrentUser();
  const userPlanId = currentUser?.planId;
  const userTier = currentUser?.tier;

  // Helper function to check if a plan is the user's current plan
  const isUserCurrentPlan = (plan: PaymentPlan): boolean => {
    const isPurchased = plan.id === userPlanId;
    return isPurchased;
  };

  // Clear any payment errors when component mounts (fresh start for plan selection)
  useEffect(() => {
    clearPaymentError();
  }, [clearPaymentError]);

  const handleClick = (plan: PaymentPlan) => {
    setSelectedPlan(plan);
    if (plan.price === 0) {
      // Handle free plan selection (no payment needed)
      console.log('Free plan selected:', plan);
      // You might want to navigate to a different page or show a success message
      navigate('/');
    } else {
      // Start payment flow for paid plans
      startPayment(plan);
    }
  };

  if (isLoading) {
    return (
      <div className={styles.priceScreen}>
        <LoadingSpinner fullScreen size="large" message="Loading plans..." />
      </div>
    );
  }

  if (hasError) {
    return (
      <div className={styles.priceScreen}>
        <div className={styles.backButtonContainer}>
          <IconButton
            type="primary"
            icon={<IoArrowBack size={20} />}
            onClick={() => navigate(-1)}
            size="medium"
            title="Go back"
          />
          <span className={styles.backButtonLabel}>Back</span>
        </div>
        <Alert severity="error" style={{ margin: '2rem' }}>
          {errorMessage || 'A payment processing error occurred'}
        </Alert>
      </div>
    );
  }

  return (
    <div className={styles.priceScreen}>
      <div className={styles.backButtonContainer}>
        <IconButton
          type="primary"
          icon={<IoArrowBack size={20} />}
          onClick={() => navigate('/')}
          size="medium"
          title="Go back"
          style={{
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease',
            background:
              'linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%)',
            border: 'none',
          }}
        />
        <span className={styles.backButtonLabel}>Back</span>
      </div>
      <h2 className={styles.heading}>Choose Your Plan</h2>

      {/* Subtle notification for plan loading issues */}
      {hasPlansError && (
        <Alert
          severity="info"
          style={{ margin: '1rem 0', fontSize: '0.875rem' }}
        >
          Using default plans. Some features may be limited.
          {plansErrorMessage && ` (${plansErrorMessage})`}
        </Alert>
      )}

      <div className={styles.priceContainer}>
        {plans && plans.length > 0 ? (
          plans.map((plan: any) => {
            const isPurchased = isUserCurrentPlan(plan);
            return (
              <div
                key={plan.id}
                className={`${styles.priceCard} ${plan.isPopular ? styles.popular : ''} ${isPurchased ? styles.purchased : ''}`}
              >
                {plan.isPopular && (
                  <div className={styles.popularBadge}>Most Popular</div>
                )}
                {isPurchased && (
                  <div className={styles.purchasedBadge}>Current Plan</div>
                )}
                <h3 className={styles.planName}>{plan.name}</h3>
                <div className={styles.price}>{formatPlanPrice(plan)}</div>
                <ul className={styles.featureList}>
                  {plan.features && plan.features.length > 0 ? (
                    plan.features.map((feature: any, featureIndex: any) => (
                      <li key={featureIndex}>{feature}</li>
                    ))
                  ) : (
                    // Fallback to show plan details when features are not available
                    <>
                      <li>{plan.tokens} tokens included</li>
                      {/* <li>Up to {plan.maxGraphs} graphs</li> */}
                      <li>{plan.description}</li>
                      {plan.supportLevel && (
                        <li>{plan.supportLevel} support</li>
                      )}
                    </>
                  )}
                </ul>
                <CustomButton
                  type="primary"
                  label={
                    isPurchased
                      ? 'Current Plan'
                      : plan.price === 0
                        ? 'Get Started'
                        : 'Choose Plan'
                  }
                  // className={styles.selectButton}
                  onClick={() => handleClick(plan)}
                  disabled={isLoading || isPurchased}
                />
              </div>
            );
          })
        ) : (
          <div className={styles.noPlansMessage}>
            <Alert severity="info">
              No pricing plans available at the moment. Please try again later.
            </Alert>
          </div>
        )}
      </div>
      {/*Displays Which plan you selected
      TODO: Remove  */}
      {selectedPlan && (
        <div className={styles.selectedPlanDetails}>
          <ul>
            <h3>Selected {selectedPlan.name} Plan</h3>
            <p>You Will Get Following Features: </p>
            {selectedPlan.features && selectedPlan.features.length > 0 ? (
              selectedPlan.features.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))
            ) : (
              // Fallback to show plan details when features are not available
              <>
                <li>{selectedPlan.tokens} tokens included</li>
                <li>Up to {selectedPlan.maxGraphs} graphs</li>
                <li>{selectedPlan.description}</li>
                {selectedPlan.supportLevel && (
                  <li>{selectedPlan.supportLevel} support</li>
                )}
              </>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default PriceScreen;
