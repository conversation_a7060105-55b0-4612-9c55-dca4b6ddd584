import React from 'react';
import {
  Box,
  Typography,
  Tooltip,
  CircularProgress,
  Chip,
} from '@mui/material';
import { styled, alpha } from '@mui/material/styles';
import { useGetCreditUsageByUserQuery } from '../../../services/creditService';
import {
  getCurrentUserId,
  getCurrentUser,
} from '../../../utils/auth/userHelpers';
import { CreditDisplayProps } from '../../../types/credit';

const CreditContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  backgroundColor:
    theme.palette.mode === 'dark'
      ? alpha(theme.palette.background.paper, 0.6)
      : alpha(theme.palette.primary.main, 0.05),
  border: `1px solid ${
    theme.palette.mode === 'dark'
      ? alpha(theme.palette.divider, 0.3)
      : alpha(theme.palette.primary.main, 0.2)
  }`,
  transition: theme.transitions.create(['background-color', 'border-color']),
  '&:hover': {
    backgroundColor:
      theme.palette.mode === 'dark'
        ? alpha(theme.palette.background.paper, 0.8)
        : alpha(theme.palette.primary.main, 0.08),
    borderColor: theme.palette.primary.main,
  },
}));

const CreditText = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  fontWeight: 500,
  color: theme.palette.text.primary,
  whiteSpace: 'nowrap',
}));

const CreditChip = styled(Chip)(({ theme }) => ({
  height: '20px',
  fontSize: '0.7rem',
  fontWeight: 600,
  '& .MuiChip-label': {
    padding: theme.spacing(0, 0.5),
  },
}));

const LoadingContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
  padding: theme.spacing(0.5, 1),
}));

const ErrorContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(0.5, 1),
  color: theme.palette.error.main,
}));

interface CreditUsageComponentProps extends Omit<CreditDisplayProps, 'userId'> {
  userId?: string;
}

const CreditUsage: React.FC<CreditUsageComponentProps> = ({
  userId: propUserId,
  showDetails = false,
  className,
}) => {
  // Get user ID from props or localStorage
  const userId = propUserId || getCurrentUserId();

  // Get current user data for tokens information
  const currentUser = getCurrentUser();
  const userTokens = currentUser?.tokens;

  const {
    data: creditData,
    isLoading,
    error,
    refetch,
  } = useGetCreditUsageByUserQuery(userId || '', {
    skip: !userId,
    pollingInterval: 30000, // Refresh every 30 seconds
  });

  // Don't render if no user ID
  if (!userId) {
    return null;
  }

  // Loading state
  if (isLoading) {
    return (
      <LoadingContainer className={className}>
        <CircularProgress size={14} />
        <CreditText>Loading...</CreditText>
      </LoadingContainer>
    );
  }

  // Error state
  if (error) {
    return (
      <Tooltip title="Failed to load credit information. Click to retry.">
        <ErrorContainer className={className} onClick={() => refetch()}>
          <CreditText>Credits: --</CreditText>
        </ErrorContainer>
      </Tooltip>
    );
  }

  // No data state
  if (!creditData) {
    return (
      <Tooltip title="No credit information available">
        <CreditContainer className={className}>
          <CreditText>Credits: --</CreditText>
        </CreditContainer>
      </Tooltip>
    );
  }

  // const { remainingCredits, totalCredits, usedCredits } = creditData;
  // const usagePercentage =
  //   totalCredits > 0 ? (usedCredits / totalCredits) * 100 : 0;

  // Determine chip color based on remaining credits
  // const getChipColor = () => {
  //   if (remainingCredits <= 0) return 'error';
  //   if (usagePercentage > 80) return 'warning';
  //   if (usagePercentage > 60) return 'info';
  //   return 'success';
  // };

  // const tooltipContent = showDetails ? (
  //   <Box>
  //     <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
  //       Credit Usage Details
  //     </Typography>
  //     <Typography variant="body2">
  //       Total Credits: {totalCredits?.toLocaleString() || 0}
  //     </Typography>
  //     <Typography variant="body2">
  //       Used Credits: {usedCredits?.toLocaleString() || 0}
  //     </Typography>
  //     <Typography variant="body2">
  //       Remaining: {remainingCredits?.toLocaleString() || 0}
  //     </Typography>
  //     <Typography
  //       variant="body2"
  //       sx={{ mt: 0.5, fontSize: '0.7rem', opacity: 0.8 }}
  //     >
  //       Usage: {usagePercentage.toFixed(1)}%
  //     </Typography>
  //   </Box>
  // ) : (
  //   `${remainingCredits?.toLocaleString() || 0} of ${totalCredits?.toLocaleString() || 0} credits remaining`
  // );

  // Create tooltip content with tokens information
  const tooltipContent = userTokens ? (
    <Box>
      <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
        Account Details
      </Typography>
      <Typography variant="body2">
        Credits: {creditData?.credits?.toLocaleString() || '--'}
      </Typography>
      <Typography variant="body2">
        Tokens: {userTokens?.toLocaleString() || '--'}
      </Typography>
    </Box>
  ) : (
    `Credits: ${creditData?.credits?.toLocaleString() || '--'}`
  );

  return (
    <Tooltip title={tooltipContent} arrow placement="bottom">
      <CreditContainer className={className}>
        <CreditText>Credits:</CreditText>
        <CreditChip
          label={creditData?.credits?.toString() || '--'}
          color={'primary'}
          size="small"
          variant="filled"
        />
      </CreditContainer>
    </Tooltip>
  );
};

export default CreditUsage;
